{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eZbnJzN6Kqr8D99u5fcJrKu3M3xQBPdxEoLw93Ej2YE=", "__NEXT_PREVIEW_MODE_ID": "a12e7d9f794fb8311dc9e638202e195e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9f497b7406e1c234eff1a65f6b555e30698cfb9b0f1a03671181dd0e493cd646", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bee935b7d512f06a8d919fe8f9cdcb3edd1a74c374b70314aace2533778a6083"}}}, "functions": {}, "sortedMiddleware": ["/"]}